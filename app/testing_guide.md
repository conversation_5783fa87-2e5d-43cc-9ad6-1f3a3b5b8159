# 🧪 Foodie Backend - Phase 1 Testing Guide

## 📋 **Testing Scope Overview**

**✅ Phase 1 Testing (Current Focus):**
- AI Integration Testing (Gemini 2.0 via OpenRouter)
- Restaurant Data Structure and Quality
- Basic API Endpoints (Non-authenticated)
- Error Handling and Fallback Systems

**⏳ Phase 2 Testing (Future):**
- User Authentication (Google OAuth + Guest Users)
- Protected Endpoints (Favorites Management)
- Session Management

### **1. Restaurant Search Endpoint (AI Integration)**

**Purpose**: Test Gemini 2.0 AI integration and restaurant data structure

#### **Example 1_1: Location-Specific Search**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q=sushi&location=toronto"
```
**✅ Expected Success Response Structure:**
```json
{
  "success": true,
  "restaurants": [
    {
      "name": "Terroni Adelaide",
      "cuisine_type": "Southern Italian",
      "rating": 4.2,
      "address": "57 Adelaide St E, Toronto, ON M5C 1K9",
      "phone": "(*************",
      "description": "A Toronto institution, Terroni Adelaide serves authentic Southern Italian cuisine in a bustling and vibrant atmosphere. Known for its pizza and pasta, this spot uses traditional ingredients and techniques to create a genuinely Italian experience.",
      "hours": {
        "monday": "11:30 AM - 11:00 PM",
        "tuesday": "11:30 AM - 11:00 PM",
        "wednesday": "11:30 AM - 11:00 PM",
        "thursday": "11:30 AM - 11:00 PM",
        "friday": "11:30 AM - 12:00 AM",
        "saturday": "11:30 AM - 12:00 AM",
        "sunday": "11:30 AM - 10:00 PM"
      }
    }
  ],
  "count": 6
}
```

**🔍 Data Structure Validation Checklist:**
- ✅ `name`: String, restaurant name
- ✅ `cuisine_type`: String, type of cuisine
- ✅ `rating`: Number, 3.0-5.0 range
- ✅ `address`: String, full address
- ✅ `phone`: String, formatted phone number
- ✅ `description`: String, 1-2 compelling sentences
- ✅ `hours`: Object, operating hours for all days

#### **Example 1_2: Missing Query Parameter**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search"
```

**❌ Expected Error Response:**
```json
{
  "error": "Missing required parameter",
  "message": "Query parameter \"q\" is required"
}
```

#### **Example 1_3: Empty Query Parameter**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q="
```

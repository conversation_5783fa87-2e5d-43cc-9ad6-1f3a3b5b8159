# 🧪 <PERSON>ie Backend - Phase 1 Testing Guide

**Version**: 1.0  
**Date**: August 3, 2025  
**Focus**: AI Integration, Restaurant Data, and Basic API Endpoints

## 📋 **Testing Scope Overview**

**✅ Phase 1 Testing (Current Focus):**
- AI Integration Testing (Gemini 2.0 via OpenRouter)
- Restaurant Data Structure and Quality
- Basic API Endpoints (Non-authenticated)
- Error Handling and Fallback Systems

**⏳ Phase 2 Testing (Future):**
- User Authentication (Google OAuth + Guest Users)
- Protected Endpoints (Favorites Management)
- Session Management

## 🚀 **Prerequisites**

### **Server Setup**
```bash
# 1. Ensure server is running
npm start

# 2. Verify server health
curl http://localhost:3000/health

# Expected Response:
# {"status":"OK","timestamp":"2025-08-03T...","environment":"development"}
```

### **Environment Variables Check**
```bash
# 3. Verify API keys are configured
node -e "
console.log('SUPABASE_URL:', process.env.SUPABASE_URL ? '✅ Set' : '❌ Missing');
console.log('SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing');
console.log('OPERROUTER_API_KEY:', process.env.OPERROUTER_API_KEY ? '✅ Set' : '❌ Missing');
"
```

### **Testing Tools Setup**
```bash
# Install helpful tools for testing
# For macOS:
brew install jq curl

# For Ubuntu/Debian:
sudo apt-get install jq curl

# For formatted JSON output (alternative to jq):
# Python 3 is usually pre-installed
python3 --version
```

## 🎯 **Phase 1 API Endpoints**

### **1. Health Check Endpoint**

**Purpose**: Verify server status and basic connectivity

```bash
# Basic Health Check
curl -X GET "http://localhost:3000/health"
```

**✅ Expected Success Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-08-03T10:30:00.000Z",
  "environment": "development"
}
```

**❌ Error Scenarios:**
- **Server Down**: `curl: (7) Failed to connect to localhost port 3000`
- **Port Conflict**: Check if another service is using port 3000

---

### **2. Restaurant Search Endpoint (AI Integration)**

**Purpose**: Test Gemini 2.0 AI integration and restaurant data structure

#### **Test Case 2.1: Basic Search Query**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q=italian"
```

#### **Test Case 2.2: Location-Specific Search**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q=sushi&location=toronto"
```

#### **Test Case 2.3: Cuisine-Specific Search**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q=mexican&location=new%20york"
```

**✅ Expected Success Response Structure:**
```json
{
  "success": true,
  "restaurants": [
    {
      "name": "Terroni Adelaide",
      "cuisine_type": "Southern Italian",
      "rating": 4.2,
      "address": "57 Adelaide St E, Toronto, ON M5C 1K9",
      "phone": "(*************",
      "description": "A Toronto institution, Terroni Adelaide serves authentic Southern Italian cuisine in a bustling and vibrant atmosphere. Known for its pizza and pasta, this spot uses traditional ingredients and techniques to create a genuinely Italian experience.",
      "hours": {
        "monday": "11:30 AM - 11:00 PM",
        "tuesday": "11:30 AM - 11:00 PM",
        "wednesday": "11:30 AM - 11:00 PM",
        "thursday": "11:30 AM - 11:00 PM",
        "friday": "11:30 AM - 12:00 AM",
        "saturday": "11:30 AM - 12:00 AM",
        "sunday": "11:30 AM - 10:00 PM"
      }
    }
  ],
  "count": 6
}
```

**🔍 Data Structure Validation Checklist:**
- ✅ `name`: String, restaurant name
- ✅ `cuisine_type`: String, type of cuisine
- ✅ `rating`: Number, 3.0-5.0 range
- ✅ `address`: String, full address
- ✅ `phone`: String, formatted phone number
- ✅ `description`: String, 1-2 compelling sentences (**NEW FIELD**)
- ✅ `hours`: Object, operating hours for all days
- ❌ `image_urls`: Should NOT be present (removed)

#### **Test Case 2.4: Missing Query Parameter**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search"
```

**❌ Expected Error Response:**
```json
{
  "error": "Missing required parameter",
  "message": "Query parameter \"q\" is required"
}
```

#### **Test Case 2.5: Empty Query Parameter**
```bash
curl -X GET "http://localhost:3000/api/restaurants/search?q="
```

---

### **3. Trending Restaurants Endpoint**

**Purpose**: Test database connectivity and restaurant data retrieval

```bash
curl -X GET "http://localhost:3000/api/restaurants/trending"
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "restaurants": [],
  "count": 0
}
```

**Note**: Initially empty as no restaurants are cached yet. After running search queries, this should populate with cached results.

---

### **4. Rate Limit Check Endpoint**

**Purpose**: Monitor OpenRouter API usage and limits

```bash
curl -X GET "http://localhost:3000/api/restaurants/rate-limit"
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "rate_limit_info": {
    "label": "sk-or-v1-86e...bca",
    "credits_used": 0,
    "credits_limit": null,
    "credits_remaining": null,
    "usage_percentage": 0,
    "is_free_tier": true,
    "unlimited": true
  },
  "free_tier_limits": {
    "requests_per_minute": 20,
    "daily_limit": 50
  },
  "status": "OK"
}
```

**🔍 Rate Limit Status Meanings:**
- **"OK"**: Normal operation, plenty of quota remaining
- **"LOW_CREDITS"**: Less than 10 credits remaining
- **"LIMIT_REACHED"**: No credits/requests remaining

---

### **5. Individual Restaurant Details**

**Purpose**: Test restaurant detail retrieval (requires restaurant ID from search results)

```bash
# First, get a restaurant ID from search results
RESTAURANT_ID=$(curl -s "http://localhost:3000/api/restaurants/search?q=italian" | python3 -c "
import sys, json
data = json.load(sys.stdin)
if data['restaurants']:
    print(data['restaurants'][0]['id'] if 'id' in data['restaurants'][0] else 'NO_ID')
else:
    print('NO_RESTAURANTS')
")

# Then get restaurant details
curl -X GET "http://localhost:3000/api/restaurants/$RESTAURANT_ID"
```

**Note**: This endpoint may return 404 initially as restaurants need to be cached first through search queries.

---

### **6. Restaurant Data Structure Deep Dive**

**Purpose**: Comprehensive validation of restaurant data structure and quality

#### **Test Case 6.1: Data Structure Validation**
```bash
# Get restaurant data and save for analysis
curl -s "http://localhost:3000/api/restaurants/search?q=fine+dining&location=toronto" > restaurant_data.json

# Analyze structure with jq (if available)
cat restaurant_data.json | jq '.restaurants[0]'

# Or with Python for detailed analysis
python3 << 'EOF'
import json

with open('restaurant_data.json', 'r') as f:
    data = json.load(f)

if data['success'] and data['restaurants']:
    restaurant = data['restaurants'][0]
    print("🔍 Restaurant Data Structure Analysis:")
    print("=====================================")

    # Check required fields
    required_fields = ['name', 'cuisine_type', 'rating', 'address', 'phone', 'description', 'hours']
    for field in required_fields:
        if field in restaurant:
            value = restaurant[field]
            print(f"✅ {field}: {type(value).__name__} - {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
        else:
            print(f"❌ {field}: MISSING")

    # Check deprecated fields
    if 'image_urls' in restaurant:
        print("❌ image_urls: SHOULD NOT BE PRESENT (deprecated)")
    else:
        print("✅ image_urls: Correctly removed")

    # Validate data quality
    print("\n📊 Data Quality Checks:")
    print("======================")

    # Rating validation
    rating = restaurant.get('rating', 0)
    if 3.0 <= rating <= 5.0:
        print(f"✅ Rating: {rating} (valid range)")
    else:
        print(f"❌ Rating: {rating} (should be 3.0-5.0)")

    # Description validation
    description = restaurant.get('description', '')
    if description and len(description) > 20:
        print(f"✅ Description: {len(description)} chars (meaningful)")
    else:
        print(f"❌ Description: Too short or missing")

    # Hours validation
    hours = restaurant.get('hours', {})
    days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    missing_days = [day for day in days if day not in hours]
    if not missing_days:
        print("✅ Hours: All 7 days present")
    else:
        print(f"❌ Hours: Missing days: {missing_days}")

else:
    print("❌ No restaurant data to analyze")
EOF
```

#### **Test Case 6.2: Multiple Restaurant Comparison**
```bash
# Test different queries to ensure variety
queries=("italian" "sushi" "mexican" "burger" "thai" "indian")

for query in "${queries[@]}"; do
    echo "Testing query: $query"
    curl -s "http://localhost:3000/api/restaurants/search?q=$query" | \
    python3 -c "
import sys, json
data = json.load(sys.stdin)
if data['success'] and data['restaurants']:
    restaurants = data['restaurants']
    print(f'  Count: {len(restaurants)}')
    cuisines = set(r.get('cuisine_type', 'Unknown') for r in restaurants)
    print(f'  Cuisine types: {list(cuisines)}')
    ratings = [r.get('rating', 0) for r in restaurants]
    print(f'  Rating range: {min(ratings):.1f} - {max(ratings):.1f}')
    print(f'  Sample: {restaurants[0].get(\"name\", \"Unknown\")}')
else:
    print('  No data returned')
"
    echo "---"
done
```

#### **Test Case 6.3: Description Quality Assessment**
```bash
# Analyze description quality across multiple searches
python3 << 'EOF'
import requests
import json

queries = ['italian toronto', 'sushi vancouver', 'mexican montreal', 'burger calgary']
base_url = 'http://localhost:3000/api/restaurants/search'

print("📝 Description Quality Analysis:")
print("===============================")

for query in queries:
    try:
        response = requests.get(f"{base_url}?q={query}")
        data = response.json()

        if data['success'] and data['restaurants']:
            print(f"\nQuery: {query}")
            for i, restaurant in enumerate(data['restaurants'][:3]):  # Check first 3
                desc = restaurant.get('description', '')
                print(f"  {i+1}. {restaurant.get('name', 'Unknown')}")
                print(f"     Description: {desc}")
                print(f"     Length: {len(desc)} chars")
                print(f"     Quality: {'✅ Good' if len(desc) > 50 else '⚠️ Short'}")
        else:
            print(f"Query: {query} - No data returned")
    except Exception as e:
        print(f"Query: {query} - Error: {e}")
EOF
```

## 🤖 **AI Integration Testing**

### **AI Response Quality Checklist**

When testing restaurant search, verify AI responses include:

**✅ Content Quality:**
- [ ] Restaurant names are realistic and varied
- [ ] Addresses include proper formatting (street, city, postal code)
- [ ] Phone numbers follow standard format: (XXX) XXX-XXXX
- [ ] **Descriptions are compelling and informative (1-2 sentences)**
- [ ] Cuisine types are accurate and diverse
- [ ] Ratings are realistic (3.0-5.0 range)
- [ ] Hours cover all 7 days with realistic times

**✅ Data Consistency:**
- [ ] All required fields are present
- [ ] No `image_urls` field in responses
- [ ] JSON structure matches expected format
- [ ] Multiple restaurants returned (typically 5-10)

### **AI vs Mock Data Detection**

**🤖 Real AI Response Indicators:**
- Specific, real-sounding restaurant names
- Accurate addresses for the requested location
- Detailed, contextual descriptions
- Varied cuisine types and ratings

**🎭 Mock Data Response Indicators:**
- Generic names like "Bella Vista Italian", "Sakura Sushi Bar"
- New York addresses (123 Main Street, etc.)
- Standard descriptions from mock data
- Limited to 3 restaurants

### **Testing AI Rate Limits**

```bash
# Test multiple rapid requests to trigger rate limiting
for i in {1..25}; do
  echo "Request $i:"
  curl -s "http://localhost:3000/api/restaurants/search?q=pizza&location=toronto" | \
    python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(f'  Restaurants: {data[\"count\"]}, First: {data[\"restaurants\"][0][\"name\"] if data[\"restaurants\"] else \"None\"}')
except:
    print('  Error parsing response')
"
  sleep 1
done
```

**Expected Behavior:**
- First ~10-20 requests: Real AI responses
- After rate limit: Graceful fallback to mock data
- No service interruption or errors

## 🔧 **Troubleshooting Guide**

### **Common Issues and Solutions**

#### **Issue 1: Server Won't Start**
```bash
# Check if port 3000 is in use
lsof -ti:3000

# Kill process if needed
kill -9 $(lsof -ti:3000)

# Restart server
npm start
```

#### **Issue 2: Database Connection Errors**
```bash
# Verify Supabase credentials
curl -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
     "$SUPABASE_URL/rest/v1/restaurants?select=count"
```

**Expected**: Should return count or proper error message, not authentication error.

#### **Issue 3: AI Integration Not Working**
```bash
# Test OpenRouter API directly
curl -X POST "https://openrouter.ai/api/v1/chat/completions" \
  -H "Authorization: Bearer $OPERROUTER_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "google/gemini-2.0-flash-exp:free",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 100
  }'
```

**Possible Issues:**
- Invalid API key: Check environment variables
- Rate limit exceeded: Wait or upgrade plan
- Network issues: Check internet connectivity

#### **Issue 4: Mock Data Always Returned**
**Symptoms**: Always getting the same 3 restaurants (Bella Vista, Sakura, Burger Joint)

**Causes & Solutions:**
1. **Rate Limit Hit**: Check `/api/restaurants/rate-limit` endpoint
2. **API Key Invalid**: Verify `OPERROUTER_API_KEY` in `.env`
3. **Network Issues**: Check server logs for connection errors

#### **Issue 5: Missing Description Field**
**Symptoms**: Restaurant objects don't include `description` field

**Solution**:
```bash
# Check database schema
curl -s "http://localhost:3000/api/restaurants/trending" | \
  python3 -c "
import sys, json
data = json.load(sys.stdin)
if data['restaurants']:
    print('Fields:', list(data['restaurants'][0].keys()))
else:
    print('No restaurants to check')
"
```

Expected fields should include `description`.

### **Performance Testing**

#### **Response Time Testing**
```bash
# Test response times
time curl -s "http://localhost:3000/api/restaurants/search?q=italian" > /dev/null

# Expected times:
# - Health check: < 100ms
# - Restaurant search (AI): 3-8 seconds
# - Restaurant search (mock): < 500ms
# - Trending restaurants: < 200ms
# - Rate limit check: < 1 second
```

#### **Concurrent Request Testing**
```bash
# Test multiple simultaneous requests
for i in {1..5}; do
  curl -s "http://localhost:3000/api/restaurants/search?q=cuisine$i" &
done
wait

echo "All requests completed"
```

## 🚀 **Automated Testing Script**

For comprehensive testing, you can use this automated script:

```bash
#!/bin/bash
# Phase 1 Automated Testing Script
# Save as: test_phase1.sh

echo "🧪 Foodie Backend - Phase 1 Automated Testing"
echo "=============================================="

BASE_URL="http://localhost:3000"
PASS=0
FAIL=0

# Function to test endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="$3"

    echo -n "Testing $name... "

    response=$(curl -s -w "%{http_code}" "$url")
    status_code="${response: -3}"
    body="${response%???}"

    if [ "$status_code" = "$expected_status" ]; then
        echo "✅ PASS (Status: $status_code)"
        ((PASS++))
        return 0
    else
        echo "❌ FAIL (Expected: $expected_status, Got: $status_code)"
        echo "   Response: $body"
        ((FAIL++))
        return 1
    fi
}

# Test 1: Health Check
test_endpoint "Health Check" "$BASE_URL/health" "200"

# Test 2: Restaurant Search - Basic
test_endpoint "Restaurant Search (Basic)" "$BASE_URL/api/restaurants/search?q=italian" "200"

# Test 3: Restaurant Search - With Location
test_endpoint "Restaurant Search (Location)" "$BASE_URL/api/restaurants/search?q=sushi&location=toronto" "200"

# Test 4: Trending Restaurants
test_endpoint "Trending Restaurants" "$BASE_URL/api/restaurants/trending" "200"

# Test 5: Rate Limit Check
test_endpoint "Rate Limit Check" "$BASE_URL/api/restaurants/rate-limit" "200"

# Test 6: Error Handling - Missing Parameter
test_endpoint "Error Handling (Missing Param)" "$BASE_URL/api/restaurants/search" "400"

# Test 7: Error Handling - Invalid Endpoint
test_endpoint "Error Handling (Invalid Endpoint)" "$BASE_URL/api/invalid-endpoint" "404"

echo ""
echo "=============================================="
echo "📊 Test Results Summary:"
echo "✅ Passed: $PASS"
echo "❌ Failed: $FAIL"
echo "📈 Success Rate: $(( PASS * 100 / (PASS + FAIL) ))%"

if [ $FAIL -eq 0 ]; then
    echo "🎉 All tests passed! Backend is ready for Phase 2."
    exit 0
else
    echo "⚠️  Some tests failed. Please review and fix issues."
    exit 1
fi
```

**Usage:**
```bash
# Make script executable
chmod +x test_phase1.sh

# Run automated tests
./test_phase1.sh
```

---

## ✅ **Phase 1 Testing Checklist**

### **Basic Functionality**
- [ ] Server starts without errors
- [ ] Health endpoint responds correctly
- [ ] Environment variables are configured
- [ ] Database connection is working

### **AI Integration**
- [ ] Restaurant search returns AI-generated results
- [ ] Fallback to mock data works when rate limited
- [ ] Rate limit endpoint shows current usage
- [ ] AI responses include all required fields
- [ ] **Descriptions are present and compelling**
- [ ] **No image_urls in responses**

### **Data Quality**
- [ ] Restaurant names are realistic
- [ ] Addresses are properly formatted
- [ ] Phone numbers follow standard format
- [ ] Ratings are in 3.0-5.0 range
- [ ] Hours include all 7 days
- [ ] Cuisine types are accurate

### **Error Handling**
- [ ] Missing query parameters return proper errors
- [ ] Invalid requests return appropriate HTTP status codes
- [ ] Rate limiting is handled gracefully
- [ ] Network errors don't crash the server

### **Performance**
- [ ] Response times are acceptable
- [ ] Server handles concurrent requests
- [ ] Memory usage remains stable
- [ ] No memory leaks during extended testing

## 🚀 **Ready for Phase 2**

Once Phase 1 testing is complete and all items in the checklist pass, the backend will be ready for Phase 2 testing, which will include:

- User authentication (Google OAuth + Guest users)
- Protected endpoints (Favorites management)
- Session management and JWT token handling
- User data isolation and security testing

**Phase 1 Success Criteria**: All basic endpoints work correctly, AI integration functions properly with fallbacks, and restaurant data structure is complete and accurate.

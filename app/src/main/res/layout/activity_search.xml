<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/search_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".SearchActivity">

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="20dp"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header Section -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingTop="16dp"
                android:paddingBottom="8dp">

                <!-- Back Arrow -->
                <ImageView
                    android:id="@+id/back_arrow"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_back_arrow_search"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="12dp"
                    android:contentDescription="Back"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:tint="@color/text_primary" />

                <!-- Search Title -->
                <TextView
                    android:id="@+id/search_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="Search"
                    android:textAppearance="@style/TextAppearance.Foodie.AppTitle"
                    app:layout_constraintStart_toEndOf="@+id/back_arrow"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Search Bar Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    style="@style/Widget.Foodie.SearchCard">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="8dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_search_active"
                            android:layout_marginEnd="8dp"
                            app:tint="@color/text_secondary" />

                        <EditText
                            android:id="@+id/search_edit_text"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Italian"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchText"
                            android:background="@android:color/transparent"
                            android:hint="Search restaurants or cuisines"
                            android:textColorHint="@color/text_secondary"
                            android:imeOptions="actionSearch"
                            android:inputType="text"
                            android:maxLines="1" />

                        <ImageView
                            android:id="@+id/clear_search"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_clear"
                            android:background="?attr/selectableItemBackgroundBorderless"
                            android:padding="2dp"
                            android:contentDescription="Clear search"
                            app:tint="@color/text_secondary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Filter Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="12dp"
                android:paddingVertical="12dp"
                android:gravity="center">

                <!-- Price Filter -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/price_filter"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:layout_marginEnd="12dp"
                    style="@style/Widget.Foodie.SearchCard"
                    app:cardCornerRadius="16dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:paddingHorizontal="16dp"
                        android:paddingEnd="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Price"
                            android:textAppearance="@style/TextAppearance.Foodie.FilterLabel"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_filter_price"
                            app:tint="@color/text_primary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Rating Filter -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/rating_filter"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:layout_marginEnd="12dp"
                    style="@style/Widget.Foodie.SearchCard"
                    app:cardCornerRadius="16dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:paddingHorizontal="16dp"
                        android:paddingEnd="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Rating"
                            android:textAppearance="@style/TextAppearance.Foodie.FilterLabel"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_filter_rating"
                            app:tint="@color/text_primary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Distance Filter -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/distance_filter"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    style="@style/Widget.Foodie.SearchCard"
                    app:cardCornerRadius="16dp">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:paddingHorizontal="16dp"
                        android:paddingEnd="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Distance"
                            android:textAppearance="@style/TextAppearance.Foodie.FilterLabel"
                            android:layout_marginEnd="8dp" />

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_filter_distance"
                            app:tint="@color/text_primary" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Search Results Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Bella Trattoria -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:paddingHorizontal="16dp"
                    android:gravity="center_vertical">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_marginEnd="16dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/search_bella_trattoria"
                        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Bella Trattoria"
                            android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.5 (1200+ reviews)"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Italian · 0.5 mi"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Pasta Paradise -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:paddingHorizontal="16dp"
                    android:gravity="center_vertical">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_marginEnd="16dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/search_pasta_paradise"
                        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Pasta Paradise"
                            android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.2 (800+ reviews)"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Italian · 1.2 mi"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Roma Ristorante -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:paddingHorizontal="16dp"
                    android:gravity="center_vertical">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_marginEnd="16dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/search_roma_ristorante"
                        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Roma Ristorante"
                            android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.0 (500+ reviews)"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Italian · 0.8 mi"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Tuscany Bistro -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:paddingHorizontal="16dp"
                    android:gravity="center_vertical">

                    <com.google.android.material.imageview.ShapeableImageView
                        android:layout_width="70dp"
                        android:layout_height="70dp"
                        android:layout_marginEnd="16dp"
                        android:scaleType="centerCrop"
                        android:src="@drawable/search_tuscany_bistro"
                        app:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.Material3.Corner.Medium" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Tuscany Bistro"
                            android:textAppearance="@style/TextAppearance.Foodie.RestaurantName"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="4.3 (1000+ reviews)"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews"
                            android:layout_marginBottom="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Italian · 1.5 mi"
                            android:textAppearance="@style/TextAppearance.Foodie.SearchReviews" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="28dp"
        android:layout_marginBottom="20dp"
        android:src="@drawable/ic_floating_action"
        android:contentDescription="Additional actions"
        app:backgroundTint="@color/fab_red"
        app:tint="@color/background_primary"
        app:fabSize="normal" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>

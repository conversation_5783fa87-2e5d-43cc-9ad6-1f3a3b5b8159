package com.example.project;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.example.project.models.AuthResponse;
import com.example.project.network.ApiClient;
import com.google.android.material.button.MaterialButton;

/**
 * Login Activity with Google OAuth and Guest login options
 * Follows the Figma design with clean, minimal UI
 */
public class LoginActivity extends AppCompatActivity {

    private static final String TAG = "LoginActivity";

    private MaterialButton googleLoginButton;
    private MaterialButton guestLoginButton;

    private ApiClient apiClient;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_login);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.login_main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        
        initializeViews();
        setupClickListeners();

        // Initialize API client
        apiClient = ApiClient.getInstance();
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    private void initializeViews() {
        googleLoginButton = findViewById(R.id.google_login_button);
        guestLoginButton = findViewById(R.id.guest_login_button);
    }
    
    private void setupClickListeners() {
        // Google Login Button
        googleLoginButton.setOnClickListener(v -> {
            Log.d(TAG, "🔐 Google login button clicked");
            handleGoogleLogin();
        });
        
        // Guest Login Button
        guestLoginButton.setOnClickListener(v -> {
            Log.d(TAG, "👤 Guest login button clicked");
            handleGuestLogin();
        });
    }
    
    /**
     * Handle Google OAuth login
     */
    private void handleGoogleLogin() {
        Log.d(TAG, "🔐 Starting Google OAuth login process");
        Toast.makeText(this, "🔐 Starting Google login...", Toast.LENGTH_SHORT).show();

        // TODO: Implement Google OAuth integration
        // This would typically involve:
        // 1. Initialize Google Sign-In client
        // 2. Start sign-in intent
        // 3. Handle result in onActivityResult
        // 4. Send ID token to backend /api/auth/google
        // 5. Store session token and navigate to MainActivity

        // For now, simulate with a test token
        String testIdToken = "test_google_id_token_" + System.currentTimeMillis();

        apiClient.authenticateWithGoogle(testIdToken, new ApiClient.AuthCallback() {
            @Override
            public void onSuccess(AuthResponse response) {
                mainHandler.post(() -> handleAuthSuccess(response, "Google OAuth"));
            }

            @Override
            public void onError(String error) {
                mainHandler.post(() -> handleLoginError("Google OAuth", error));
            }
        });
    }
    
    /**
     * Handle guest login
     */
    private void handleGuestLogin() {
        Log.d(TAG, "👤 Starting guest login process");
        Toast.makeText(this, "👤 Logging in as guest...", Toast.LENGTH_SHORT).show();

        // Get device ID for guest authentication
        String deviceId = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);

        apiClient.authenticateAsGuest(deviceId, new ApiClient.AuthCallback() {
            @Override
            public void onSuccess(AuthResponse response) {
                mainHandler.post(() -> handleAuthSuccess(response, "Guest"));
            }

            @Override
            public void onError(String error) {
                mainHandler.post(() -> handleLoginError("Guest", error));
            }
        });
    }
    
    /**
     * Handle successful authentication
     */
    private void handleAuthSuccess(AuthResponse response, String loginType) {
        Log.d(TAG, "✅ " + loginType + " login successful: " + response.toString());
        Toast.makeText(this, "✅ " + loginType + " login successful!", Toast.LENGTH_SHORT).show();

        // TODO: Store authentication tokens securely
        // SharedPreferences or encrypted storage would be used here
        // For now, just log the user info
        if (response.getUser() != null) {
            Log.d(TAG, "👤 User info: " + response.getUser().toString());
        }

        // Navigate to MainActivity
        Intent intent = new Intent(LoginActivity.this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
    
    /**
     * Handle login error
     */
    private void handleLoginError(String loginType, String error) {
        Log.e(TAG, "❌ " + loginType + " login failed: " + error);
        Toast.makeText(this, "❌ " + loginType + " login failed: " + error, Toast.LENGTH_LONG).show();
    }
}
